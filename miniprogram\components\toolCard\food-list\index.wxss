.food-card-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 16rpx 0rpx;
}

.food-card {
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
}

.food-image {
  width: 100%;
  height: 260rpx;
  border-radius: 8rpx;
}

.food-info {
  margin-top: 8rpx;
  text-align: center;
}

.food-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.food-price {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}

.add-button {
  margin-top: 12rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #60a5fa;
  color: #ffffff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
}
